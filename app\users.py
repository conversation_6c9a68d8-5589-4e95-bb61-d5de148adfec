import uuid
from typing import Optional

from fastapi import Depends, Request
from fastapi_users import BaseUserManager, FastAPIUsers, UUIDIDMixin, models
from fastapi_users.authentication import (
    AuthenticationBackend,
    CookieTransport,
    JWTStrategy,
)
from fastapi_users.db import SQLAlchemyUserDatabase

from app.db import User, get_user_db
from app.config import SECRET, BASE_URL, is_development, is_production
from app.mail_utils import send_reset_email, send_authorization_email
from app.user_settings_db import create_user_settings_entry


class UserManager(UUIDIDMixin, BaseUserManager[User, uuid.UUID]):
    reset_password_token_secret = SECRET
    verification_token_secret = SECRET
    require_verification = True

    async def on_after_register(self, user: User, request: Optional[Request] = None):
        print(f"User {user.id} has registered.")

    async def on_after_forgot_password(
        self, user: User, token: str, request: Optional[Request] = None):
        reset_url = f"{BASE_URL}/reset-password?token={token}"
        await send_reset_email(user.email, reset_url)

    async def on_after_request_verify(
        self, user: User, token: str, request: Optional[Request] = None):
        if is_development():
            print(f"Verification requested for user {user.id}. Verification token: {token}")
        else:
            print(f"Verification requested for user {user.id}")
        verification_url = f"{BASE_URL}/verify_email?token={token}"
        try:
            if is_development():
                print(f"user.email: {user.email}")
            await send_authorization_email(user.email, verification_url)
            print(f"Verification email sent successfully to {user.email}")
        except Exception as e:
            print(f"Error in on_after_request_verify: {e}")
            if is_development():
                print(f"Error type: {type(e)}")
                import traceback
                traceback.print_exc()

    async def on_after_verify(
        self, user: User, request: Optional[Request] = None):
        print(f"User {user.id} has been verified.")

        # Activate the user after successful verification
        await self.user_db.update(user, {"is_active": True})

        # Create user settings entry after successful verification
        try:
            await create_user_settings_entry(email=user.email, name="", info="")
            print(f"User settings created for email: {user.email}")
        except Exception as e:
            print(f"Error creating user settings for {user.email}: {e}")

async def get_user_manager(user_db: SQLAlchemyUserDatabase = Depends(get_user_db)):
    yield UserManager(user_db)

# Environment-aware cookie security configuration
cookie_transport = CookieTransport(
    cookie_name="auth",
    cookie_max_age=86400,  # 24 hours
    cookie_secure=is_production(),  # HTTPS only in production
    cookie_httponly=True,  # Prevent XSS attacks
    cookie_samesite="strict" if is_production() else "lax"  # CSRF protection
)

def get_jwt_strategy() -> JWTStrategy[models.UP, models.ID]:
    # Shorter token lifetime in production for better security
    lifetime = 3600 if is_production() else 86400  # 1 hour in prod, 24 hours in dev
    return JWTStrategy(
        secret=SECRET,
        lifetime_seconds=lifetime,
        algorithm="HS256"  # Explicit algorithm specification
    )

auth_backend = AuthenticationBackend(
    name="jwt",
    transport=cookie_transport,
    get_strategy=get_jwt_strategy,
) # type: ignore

fastapi_users = FastAPIUsers[User, uuid.UUID](get_user_manager, [auth_backend])

current_user = fastapi_users.current_user(optional=True, active=True, verified=True)
