uv init

The first time you run the add command, UV creates a new virtual environment in the current working directory and installs the specified dependencies.

uv add fastapi fastapi-users[sqlalchemy,oauth] uvicorn[standard] aiosqlite Jinja2

fastapi
fastapi-users[sqlalchemy,oauth]
uvicorn[standard]
aiosqlite
Jinja2
jinja2-fragments
jinja-partials
python-multipart
requests
datastar-py
fpdf2
fastapi-mail


uv remove email-validator
uv add djlint
uv add requests
uv remove python-multipart


uv add fpdf2
uv add types-fpdf2


pip install alembic


13818

What's the best way to handle persistant storage? We have 3 database files.
Use external settings and configurations in environment variables? #fetching: https://fastapi.tiangolo.com/advanced/settings/ and also https://render.com/docs/configure-environment-variables  https://render.com/docs/configure-environment-variables
Analyze my app and update the database related code so it uses the Fastapi standards #fetching: https://fastapi.tiangolo.com/tutorial/sql-databases/. Use also Alembic. Ask me if unsure what to do.


How to Deploy FastAPI to Render https://cleverzone.medium.com/how-to-deploy-fastapi-to-render-8204c1443e2e    https://render.com/docs/deploy-fastapi
Render uv https://render.com/docs/uv-version




Analyze my project, check the security features of my app and propose changes as well as addittions to make my app more secure.
Include recommendations for the FastAPI framework, FastAPI-Users, input fields etc. Don't edit yet, first propose.




