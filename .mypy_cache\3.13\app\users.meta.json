{"data_mtime": 1751369124, "dep_lines": [5, 6, 11, 13, 14, 15, 16, 1, 2, 4, 5, 43, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi_users.models", "fastapi_users.authentication", "fastapi_users.db", "app.db", "app.config", "app.mail_utils", "app.user_settings_db", "uuid", "typing", "<PERSON><PERSON><PERSON>", "fastapi_users", "traceback", "builtins", "_frozen_importlib", "_typeshed", "abc", "fastapi.param_functions", "fastapi_users.authentication.backend", "fastapi_users.authentication.strategy", "fastapi_users.authentication.strategy.base", "fastapi_users.authentication.strategy.jwt", "fastapi_users.authentication.transport", "fastapi_users.authentication.transport.base", "fastapi_users.authentication.transport.cookie", "fastapi_users.db.base", "fastapi_users.fastapi_users", "fastapi_users.manager", "fastapi_users.password", "fastapi_users_db_sqlalchemy", "pydantic", "pydantic.types", "sqlalchemy", "sqlalchemy.ext", "sqlalchemy.ext.asyncio", "sqlalchemy.ext.asyncio.base", "sqlalchemy.ext.asyncio.session", "sqlalchemy.inspection", "sqlalchemy.orm", "sqlalchemy.orm.decl_api", "starlette", "starlette.requests"], "hash": "4cf3a7ecc4f18d36cef48af21328a5f8c7603214", "id": "app.users", "ignore_all": false, "interface_hash": "8fb708d83fc1b022bdbb2c92db7d174f678c3d2c", "mtime": 1751378188, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Local\\Projects\\BioCleaning\\app\\users.py", "plugin_data": null, "size": 3162, "suppressed": [], "version_id": "1.15.0"}