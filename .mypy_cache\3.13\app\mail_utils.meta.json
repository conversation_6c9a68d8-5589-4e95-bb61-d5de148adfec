{"data_mtime": 1751362132, "dep_lines": [2, 1, 3, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["app.mail_config", "fastapi_mail", "<PERSON><PERSON><PERSON>", "builtins", "_frozen_importlib", "abc", "enum", "fastapi.datastructures", "fastapi_mail.config", "fastapi_mail.fastmail", "fastapi_mail.schemas", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "pydantic_settings", "pydantic_settings.main", "starlette", "starlette.datastructures", "typing"], "hash": "8a681fb74636ca87319e1a573d592c9f81e3304f", "id": "app.mail_utils", "ignore_all": false, "interface_hash": "88b199af1f78b991d5a5671b85a2e1fa7d9afd79", "mtime": 1751376062, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Local\\Projects\\BioCleaning\\app\\mail_utils.py", "plugin_data": null, "size": 1905, "suppressed": [], "version_id": "1.15.0"}