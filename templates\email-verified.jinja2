{% extends "base.jinja2" %}

{% block title %}Email Verified{% endblock %}
{% block content %}

<div id="content-div">
  {# TITLE SINGLE #}
  <div>
    <style>
        me {
            width: 100%;
            text-align: center;
            padding-bottom: 8px;
        }

        me div {
            padding-bottom: 14px;
            font-family: 'Noto Serif', serif;
            font-size: 24px;
            font-weight: 400;
            font-stretch: semi-condensed;
            font-style: italic;
        }

        me hr {
            display: block;
            height: 1px;
            border: 0;
            border-top: 1px solid var(--color-hr-lines);
            margin: 0;
            padding: 0;
        }
    </style>
    <div>Email Verified!</div>
    <hr />
  </div>

  <div>
    <style>
        me {
            padding-bottom: 40px;
            padding-top: 40px;
            text-align: center;
        }

        .animated-points {
            display: inline-block;
            animation: blink 1s steps(4, end) infinite;
        }

        @keyframes blink {
            0%, 100% {
                opacity: 1;
            }
            25% {
                opacity: 0;
            }
        }
    </style>
    <span>Your email has been successfully verified! Redirecting to login</span>
    <span class="animated-points">.</span>
    <span class="animated-points">.</span>
    <span class="animated-points">.</span>
  </div>

</div>

<script>
  // Redirect to login page after 2 seconds
  setTimeout(function() {
    window.location.href = '/login_form';
  }, 2000);
</script>

{% endblock content %}
