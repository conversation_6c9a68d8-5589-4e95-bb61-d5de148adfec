<div>
  <style>
      me {
          display: flex;
          flex-direction: column-reverse;
          padding-top: 64px;
          padding-bottom: 20px;
          flex-basis: 100%;
      }

      me select {
        all: unset;
        font-family: 'Noto Sans', sans-serif;
        font-size: 18px;
        font-weight: 400;
        color: black;
        height: 24px;
        border: 0;
        background-color: transparent;
        border-bottom: 1px solid var(--color-input-lines);
        background: url(/static/images/icons8-arrow-down-64.png) no-repeat right var(--color-background-bright);
        background-size: 26px 24px;

        &:focus {
            outline: 0;
            border-bottom: 1px solid var(--color-input-lines);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 15px;
                color: var(--color-text-dark);
                transform: translateY(-24px);
            }
        }

        &:valid:not([value=""]) {
            border-bottom: 1px solid hsl(55, 96%, 38%);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 15px;
                color: var(--color-text-dark);
                transform: translateY(-24px);
            }
        }

        &:disabled {
            color: #555;
            border-bottom: 1px solid #555;
        }
      }

      me .input-label.float-label {
        font-family: 'Noto Serif', serif;
        font-style: italic;
        font-size: 15px;
        color: var(--color-text-dark);
        transform: translateY(-24px);
      }
      select:disabled + .input-label.float-label {
        color: #555;
      }

      me .input-label,
      .input-label {
          pointer-events: none;
      }
  </style>

  <select class="custom-float-select" name="{{ namealwayschange }}" id="{{ namealwayschange }}" onchange="this.setAttribute('value', this.value);" {% if required is not defined or required %} required{% endif %} >
    <option value=""></option>
    {% for option in optionslist %}
      {% if option is mapping %}
        <option value="{{ option.value }}"{% if option.selected %} selected{% endif %}>{{ option.value }}</option>
      {% else %}
        <option value="{{ option }}">{{ option }}</option>
      {% endif %}
    {% endfor %}
  </select>
  <label class="input-label custom-float-label">
    <style>
        me {
            color: black;
            position: absolute;
            transition: .15s ease;
            margin-bottom: 6px;
        }
        select:disabled + .input-label {
            color: #555;
        }
        select:disabled + .input-label.float-label {
            color: #555;
        }
    </style>
    {{ label | safe }}
  </label>
</div>

<script>
document.addEventListener("DOMContentLoaded", function() {
  // For all custom-float-select elements on the page
  document.querySelectorAll('.custom-float-select').forEach(function(select) {
    const label = select.nextElementSibling && select.nextElementSibling.classList.contains('custom-float-label') ? select.nextElementSibling : null;
    function updateLabel() {
      if (select && label) {
        if (select.value && select.value !== "") {
          label.classList.add('float-label');
        } else {
          label.classList.remove('float-label');
        }
      }
    }
    // Set value attribute for CSS compatibility
    select.setAttribute('value', select.value);
    updateLabel();
    select.addEventListener('change', function() {
      select.setAttribute('value', select.value);
      updateLabel();
    });
    // Also update label on page load in case value is set by browser autofill or server
    updateLabel();
  });
});
</script>

