<!DOCTYPE html>
<html lang="en">
  <head>
    <title>
      {% block title %}{% endblock %}
    - Hydro Consulting Applications</title>
    <meta charset="utf-8" />

    <!-- Font Awesome Icons https://fontawesome.com/icons -->
    <link rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
          integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
          crossorigin="anonymous"
          referrerpolicy="no-referrer" />

    <!-- Νοτο Variable Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans:ital,wdth,wght@0,62.5..100,100..900;1,62.5..100,100..900&family=Noto+Serif:ital,wdth,wght@0,62.5..100,100..900;1,62.5..100,100..900&display=swap"
          rel="stylesheet" />

    <!-- Static CSS -->
    <link href="{{ url_for('static', path='/css/styles.css') }}" rel="stylesheet" />

    <!-- favicon -->
    <link id="favicon"
          rel="icon"
          type="image/x-icon"
          href="{{ url_for('static', path='/images/favicon.ico') }}" />

     <!-- DATASTAR -->
    <script type="module" src="https://cdn.jsdelivr.net/gh/starfederation/datastar@v1.0.0-beta.11/bundles/datastar.js"></script>

    <!-- css-scope-inline -->
    <!-- <script src="{{ url_for('static', path='/js/css-scope-inline-script.js') }}"></script> -->
    <script src="https://cdn.jsdelivr.net/gh/gnat/css-scope-inline@main/script.js"></script>

    <!-- Random ID Generator -->
    <script>
        function randomId() {
            return Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
        }
    </script>

  </head>

  <body class="Site" id="body-div">
    <div data-signals="{signalShowContentTransition: true}">
      <style>
          me {
              box-sizing: border-box;
              min-height: 100%;
              display: flex;
              flex-direction: column;
          }
      </style>
      <header>
        <style>
            me {
                background-color: var(--color-background-dark);
            }
        </style>
        <div class="container">
          <section>
            <style>
                me {
                    height: 42px;
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    align-items: center;

                }
            </style>
            <div>
              <style>
                  me {
                      font-family: 'Noto Serif', serif;
                      font-size: 20px;
                      font-weight: 500;
                      font-stretch: condensed;
                      width: auto;
                      color: var(--color-text-bright);
                      display: flex;
                      flex: 1;
                  }
              </style>
              <img class="navbarlogo" src="{{ url_for('static', path='/images/Hydro_Logomark.png') }}" alt="Logo" />
              <div>Hydro Consulting</div>
            </div>
            <div>
              <style>
                  me {
                      font-family: 'Noto Sans', sans-serif;
                      font-size: 22px;
                      font-weight: 200;
                      font-stretch: semi-condensed;
                      color: var(--color-text-bright);
                      margin: auto;
                  }
              </style>
              (1) Applications
            </div>

            <div>
              <style>
                  me {
                      display: flex;
                      column-gap: 20px;
                      margin-left: auto;
                  }
              </style>

              <button popovertarget="colors-popover">
                <style>
                    me {
                        background-color: transparent;
                        cursor: pointer;
                        border: 0px;
                    }
                </style>
                <i class="fa-solid fa-palette">
                  <style>
                      me {
                          font-size: 22px;
                          color: var(--color-text-bright);
                          margin-top: 2px;
                      }
                  </style>
                </i>
              </button>

              <div popover id="colors-popover">
                <style>
                    me {
                        width: 400px;
                        background-color: var(--color-background-dark);
                        color: var(--color-text-bright);
                        border: 1px solid var(--color-background-middle);
                        border-width: 1px;
                        border-radius: 16px;
                        padding: 38px 38px;
                        cursor: pointer;
                        text-align: center;
                        font-size: 1.16rem;
                    }
                </style>

                <fieldset>
                  <style>
                      me {
                          display: flex;
                          flex: 1;
                          align-items: center;
                          justify-content: center;
                          column-gap: 10px;
                      }
                  </style>
                  <legend>Color themes:</legend>
                  <div>
                    <input type="radio" id="orange" name="colortheme" value="orange" checked />
                    <label for="orange">Orange</label>
                  </div>
                  <div>
                    <input type="radio" id="purple" name="colortheme" value="purple" />
                    <label for="purple">Purple</label>
                  </div>
                  <div>
                    <input type="radio" id="brown" name="colortheme" value="brown" />
                    <label for="brown">Brown</label>
                  </div>
                  <div>
                    <input type="radio" id="green" name="colortheme" value="green" />
                    <label for="brown">Green</label>
                  </div>
                  <div>
                    <input type="radio" id="dark" name="colortheme" value="dark" />
                    <label for="brown">Dark</label>
                  </div>
                </fieldset>

                <script>
                    function applyTheme(theme) {
                        const root = document.documentElement;
                        if (theme === 'brown') {
                            root.style.setProperty('--color-background-dark', '#4a3f2c');
                            root.style.setProperty('--color-background-middle', '#b19c77');
                            root.style.setProperty('--color-background-bright', '#e9e3d8');
                            root.style.setProperty('--color-text-black', '#000000');
                            root.style.setProperty('--color-text-dark', '#6b4400');
                            root.style.setProperty('--color-text-middle', '#b19c77');
                            root.style.setProperty('--color-text-bright', '#d7d2c9');
                            root.style.setProperty('--color-text-brighter', '#e9e3d8');
                            root.style.setProperty('--color-border-middle', '#c6b18e');
                            root.style.setProperty('--color-input-lines', '#6b4400');
                            root.style.setProperty('--color-hr-lines', '#6b4400');
                            root.style.setProperty('--color-selected-green', 'hsl(55, 96%, 38%)');
                            root.style.setProperty('--color-selected-red', '#ff3300');
                            root.style.setProperty('--color-disabled', '#6f6f6f');
                            root.style.setProperty('--color-list-background', 'rgba(250, 167, 0, 0.07)');
                            root.style.setProperty('--color-error-title', '#ff6a40');
                        } else if (theme === 'purple') {
                            root.style.setProperty('--color-background-dark', '#663b18');
                            root.style.setProperty('--color-background-middle', '#e0dbcd');
                            root.style.setProperty('--color-background-bright', '#f6f5ee');
                            root.style.setProperty('--color-text-black', '#000000');
                            root.style.setProperty('--color-text-dark', '#7c3800');
                            root.style.setProperty('--color-text-middle', '#615637');
                            root.style.setProperty('--color-text-bright', '#f6e1c5');
                            root.style.setProperty('--color-text-brighter', '#f6e1c5');
                            root.style.setProperty('--color-border-middle', 'rgba(102, 59, 24, 0.2)');
                            root.style.setProperty('--color-input-lines', '#994500');
                            root.style.setProperty('--color-hr-lines', '#6b4400');
                            root.style.setProperty('--color-selected-green', 'hsl(55, 96%, 38%)');
                            root.style.setProperty('--color-selected-red', 'ff3300');
                            root.style.setProperty('--color-disabled', '#6f6f6f');
                            root.style.setProperty('--color-list-background', 'rgba(250, 167, 0, 0.07)');
                            root.style.setProperty('--color-error-title', '#ff6a40');
                        } else if (theme === 'green') {
                            root.style.setProperty('--color-background-dark', '#282F3B');
                            root.style.setProperty('--color-background-middle', '#93BDAC');
                            root.style.setProperty('--color-background-bright', '#F4EDDF');
                            root.style.setProperty('--color-text-black', '#000000');
                            root.style.setProperty('--color-text-dark', '#85774B');
                            root.style.setProperty('--color-text-middle', '#85774B');
                            root.style.setProperty('--color-text-bright', '#CBE4DE');
                            root.style.setProperty('--color-text-brighter', '#f6e1c5');
                            root.style.setProperty('--color-border-middle', '#A7C9BC');
                            root.style.setProperty('--color-input-lines', '#85774B');
                            root.style.setProperty('--color-hr-lines', '#6b4400');
                            root.style.setProperty('--color-selected-green', 'hsl(55, 96%, 38%)');
                            root.style.setProperty('--color-selected-red', 'ff3300');
                            root.style.setProperty('--color-disabled', '#6f6f6f');
                            root.style.setProperty('--color-list-background', 'rgba(250, 167, 0, 0.07)');
                            root.style.setProperty('--color-error-title', '#ff6a40');
                        } else if (theme === 'dark') {
                            root.style.setProperty('--color-background-dark', '#000000');
                            root.style.setProperty('--color-background-middle', '#43494E');
                            root.style.setProperty('--color-background-bright', '#202329');
                            root.style.setProperty('--color-text-black', '#F0DAC6');
                            root.style.setProperty('--color-text-dark', '#FEBA9C');
                            root.style.setProperty('--color-text-middle', '#FF6631');
                            root.style.setProperty('--color-text-bright', '#F0DAC6');
                            root.style.setProperty('--color-text-brighter', '#F0DAC6');
                            root.style.setProperty('--color-border-middle', 'rgba(252, 106, 41, 0.4)');
                            root.style.setProperty('--color-input-lines', '#FEBA9C');
                            root.style.setProperty('--color-hr-lines', '#6b4400');
                            root.style.setProperty('--color-selected-green', 'hsl(55, 96%, 38%)');
                            root.style.setProperty('--color-selected-red', '#ff3300');
                            root.style.setProperty('--color-disabled', '#6f6f6f');
                            root.style.setProperty('--color-list-background', 'rgba(250, 167, 0, 0.07)');
                            root.style.setProperty('--color-error-title', '#ff6a40');
                        } else {
                            root.style.setProperty('--color-background-dark', '#472c07');
                            root.style.setProperty('--color-background-middle', '#d89c52');
                            root.style.setProperty('--color-background-bright', '#f6e1c5');
                            root.style.setProperty('--color-text-black', '#000000');
                            root.style.setProperty('--color-text-dark', '#8b4f06');
                            root.style.setProperty('--color-text-middle', '#945200');
                            root.style.setProperty('--color-text-bright', 'rgb(222, 203, 177)');
                            root.style.setProperty('--color-text-brighter', '#f6e1c5');
                            root.style.setProperty('--color-border-middle', '#e5ad63');
                            root.style.setProperty('--color-input-lines', '#d89c52');
                            root.style.setProperty('--color-hr-lines', '#6b4400');
                            root.style.setProperty('--color-selected-green', 'hsl(55, 96%, 38%)');
                            root.style.setProperty('--color-selected-red', 'ff3300');
                            root.style.setProperty('--color-disabled', '#6f6f6f');
                            root.style.setProperty('--color-list-background', 'rgba(250, 167, 0, 0.07)');
                            root.style.setProperty('--color-error-title', '#ff6a40');
                        }
                    }

                    // Restore saved theme on page load
                    const savedTheme = localStorage.getItem('selectedTheme');
                    if (savedTheme) {
                        applyTheme(savedTheme);
                        // Check the corresponding radio button
                        const radioButton = document.querySelector(`input[name="colortheme"][value="${savedTheme}"]`);
                        if (radioButton) {
                            radioButton.checked = true;
                        }
                    }

                    // Add event listeners to radio buttons
                    document.querySelectorAll('input[name="colortheme"]').forEach(radio => {
                        radio.addEventListener('change', (event) => {
                            const selectedTheme = event.target.value;
                            applyTheme(selectedTheme);
                            // Save theme preference to localStorage
                            localStorage.setItem('selectedTheme', selectedTheme);
                        });
                    });
                </script>

              </div>
              <div>
                {% if user is defined and user is not none %}
                <div>
                  <style>
                  me {
                    position: relative; /* so dropdown is positioned relative to this */
                    display: inline-block;
                  }
                  </style>
                  <input type="checkbox" id="menu">
                  <label for="menu" class="icon">
                      <div class="menu"></div>
                  </label>
                  <div class="dropdown">
                    <div class="dropdown-item" onclick="location.href='/userinfo'">
                      <span class="icon"><i class="fa-solid fa-user"></i></span>
                      <span class="text">User Info</span>
                    </div>
                    <div class="dropdown-item" onclick="location.href='/usercustomers'">
                      <span class="icon"><i class="fa-solid fa-users"></i></span>
                      <span class="text">Customers</span>
                    </div>
                    <div class="dropdown-item" onclick="location.href='/userprojects'">
                      <span class="icon"><i class="fa-solid fa-diagram-project"></i></span>
                      <span class="text">Projects</span>
                    </div>
                    <div class="dropdown-item" onclick="location.href='/logout'">
                      <span class="icon"><i class="fa-solid fa-right-from-bracket"></i></span>
                      <span class="text">Log out</span>
                    </div>
                  </div>
                </div>

                {% else %}
                <button onclick="location.href='/login_form'">
                  <style>
                      me {
                          font-family: 'Noto Sans', sans-serif;
                          font-size: 16px;
                          font-weight: 600;
                          font-stretch: condensed;
                          background-color: transparent;
                          color: var(--color-text-bright);
                          border: 1px solid var(--color-text-bright);
                          padding: 2px 14px;
                          cursor: pointer;
                          border-width: 1px;
                          border-radius: 6px;
                          margin-left: auto;
                      }

                      me:hover {
                          background-color: var(--color-background-bright);
                          color: var(--color-text-dark);
                      }
                  </style>
                  Log in
                </button>

                {% endif %}
              </div>


            </div>
          </section>
        </div>
      </header>

      <main>
        <style>
            me {
                flex: 1;
            }
        </style>
        <div class="container">
          <div data-class-slidetransition="$signalShowContentTransition" class="content-div slidetransition">
            <div>
              <style>
                  me {
                      display: flex;
                      flex-direction: column;
                      width: auto;
                      max-width: 820px;
                      background-color: var(--color-background-bright);
                      border-radius: 6px;
                      padding-top: 46px;
                      padding-bottom: 16px;
                      padding-left: clamp(18px, 20%, 272px);
                      padding-right: clamp(18px, 20%, 272px);
                  }
              </style>
              <div id="content-div">
                {% block content %}
                {% endblock %}
              </div>
            </div>
          </div>

        </div>
      </main>

      <div>
        <style>
            me {
                width: 100%;
                text-align: center;
                padding-bottom: 20px;
                align-items: center;
            }
        </style>

        <div class="container">
          <hr class="footer-hr" />
          <div>
            <style>
                me {
                    font-family: 'Noto Serif', serif;
                    font-size: 20px;
                    font-weight: 500;
                    font-stretch: condensed;
                    width: auto;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex: 1;
                }
            </style>
            <img class="footerlogo" src="{{ url_for('static', path='/images/Hydro_Logomark.png') }}" alt="Logo" />
            <div>Hydro Consulting S.A.</div>
          </div>
          <div>
            <style>
                me {
                    font-family: 'Noto Sans', sans-serif;
                    font-size: 15px;
                    font-weight: 300;
                    font-stretch: normal;
                }
            </style>
            © Copyright 2018-2025. All Rights Reserved.
          </div>
          <div>
            <style>
                me {
                    width: auto;
                    display: flex;
                    gap: 10px;
                    align-items: center;
                    justify-content: center;
                    margin-top: 8px;
                }
            </style>
            <a href=""> <i class="fa-brands fa-square-facebook"></i></a>
            <a href=""> <i class="fa-brands fa-linkedin"></i></a>
            <a href=""> <i class="fa-solid fa-envelope"></i></a>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
