[project]
name = "biocleaning"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "aiosqlite>=0.21.0",
    "fastapi>=0.115.12",
    "fastapi-users[oauth,sqlalchemy]>=14.0.1",
    "jinja2-fragments>=1.8.0",
    "jinja2>=3.1.6",
    "uvicorn[standard]>=0.34.0",
    "jinja-partials>=0.2.1",
    "djlint>=1.36.4",
    "requests>=2.32.3",
    "datastar-py>=0.4.4",
    "fastapi-mail>=1.5.0",
    "fpdf2>=2.8.3",
    "types-fpdf2>=2.8.3.20250516",
]
[tool.djlint]
ignore="H025,H026,H030,H031,H006,H014"
