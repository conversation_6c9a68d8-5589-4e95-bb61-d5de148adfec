<div>
  <style>
      me {
          display: flex;
          flex-direction: column-reverse;
          padding-top: 94px;
          padding-bottom: 20px;
          flex-basis: 100%;
      }

      me input {
        font-family: 'Noto Sans', sans-serif;
        font-size: 18px;
        color: black;
        height: 25px;
        border: 0;
        z-index: 1;
        background-color: transparent;
        border-bottom: 1px solid var(--color-input-lines);

        &:focus {
            outline: 0;
            border-bottom: 1px solid var(--color-input-lines);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 15px;
                color: var(--color-text-dark);
                transform: translateY(-1.5rem);
            }
        }

        &:valid {
            border-bottom: 1px solid hsl(55, 96%, 38%);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 15px;
                color: var(--color-text-dark);
                transform: translateY(-1.5rem);
            }
        }

        &:not(:placeholder-shown):invalid {
            border-bottom: 1px solid #ff3300;

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 15px;
                color: var(--color-text-dark);
                transform: translateY(-1.5rem);
            }
        }

        &:not(:placeholder-shown):not(:focus):invalid~.input-group__error {
            visibility: visible;
            opacity: 1;
        }

        &:disabled {
            color: #555;
            border-bottom: 1px solid #555;
        }
        &:disabled + .input-label {
            color: #555;
        }
        &:disabled + .input-label.float-label {
            color: #555;
        }
      }

      me .input-group__error {
          color: #ae2300;
          display: block;
          position: relative;
          visibility: hidden;
          opacity: 0;
          margin-left: 10px;
          margin-top: 1px;
          margin-bottom: -52px;
          font-family: 'Noto Serif', serif;
          font-size: 14px;
          transition: all 0.3s ease-out;
      }

  </style>

  <input pattern="{{ pattern | default('') }}" placeholder="" type="{{ type | default('text') }}" value="{{ value | default('') }}" name="{{ namealwayschange }}" id="{{ namealwayschange }}" {% if required is not defined or required %} required{% endif %} />
  <label class="input-label">
    <style>
        me {
            color: black;
            position: absolute;
            transition: .15s ease;
            margin-bottom: 6px;
        }
        .input-label:disabled, input:disabled + .input-label {
            color: #555;
        }
        input:disabled + .input-label.float-label {
            color: #555;
        }
    </style>
    {{ label | safe }}
  </label>

  <span class="input-group__error">{{ errormessage }}</span>
</div>


{# Here are the different input types you can use in HTML:

<input type="button">
<input type="checkbox">
<input type="color">
<input type="date">
<input type="datetime-local">
<input type="email">
<input type="file">
<input type="hidden">
<input type="image">
<input type="month">
<input type="number">
<input type="password">
<input type="radio">
<input type="range">
<input type="reset">
<input type="search">
<input type="submit">
<input type="tel">
<input type="text">
<input type="time">
<input type="url">
<input type="week">

#}


{# The value attribute specifies the value of an <input> element.
The value attribute is used differently for different input types:
For "button", "reset", and "submit" - it defines the text on the button
For "text", "password", and "hidden" - it defines the initial (default) value of the input field
For "checkbox", "radio", "image" - it defines the value associated with the input (this is also the value that is sent on submit) #}