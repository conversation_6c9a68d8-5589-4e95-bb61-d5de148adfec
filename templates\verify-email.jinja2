{% extends "base.jinja2" %}

{% block title %}Verify Email{% endblock %}
{% block content %}

<div id="content-div">
  {# TITLE SINGLE #}
  <div>
    <style>
        me {
            width: 100%;
            text-align: center;
            padding-bottom: 8px;
        }

        me div {
            padding-bottom: 14px;
            font-family: 'Noto Serif', serif;
            font-size: 24px;
            font-weight: 400;
            font-stretch: semi-condensed;
            font-style: italic;
        }

        me hr {
            display: block;
            height: 1px;
            border: 0;
            border-top: 1px solid #a8a8a8;
            margin: 0;
            padding: 0;
        }
    </style>
    <div>Verify Your Email</div>
    <hr />
  </div>

  <div>
    <style>
        me {
            padding-bottom: 40px;
            padding-top: 40px;
            text-align: center;
        }
    </style>
    Click the button below to verify your email address and complete your registration.
    <br />
    <br />
  </div>

  <form data-signals="{_verify_submit_button_disable:false}" data-on-submit="$_verify_submit_button_disable = true;@post('/verify_email', {contentType: 'form'})">
    <input type="hidden" name="token" value="{{ token }}">
    
    {# SUBMIT BUTTON #}
    <button type="submit" data-attr-disabled="$_verify_submit_button_disable">
      <style>
          me {
              height: 40px;
              margin-top: 2rem;
              width: 100%;
              display: block;
              background-color: transparent;
              color: var(--color-text-dark);
              border: 1px solid var(--color-text-dark);
              cursor: pointer;
              transition: .5s ease;
              border-width: 1px;
              border-style: solid;
              border-radius: 0px;
              font-family: 'Noto Sans', sans-serif;
              font-size: 18px;
              font-weight: 300;
          }

          me:hover {
              background-color: var(--color-text-dark);
              color: var(--color-background-bright);
          }

          me:disabled {
              opacity: 0.6;
              cursor: not-allowed;
          }

          me:disabled:hover {
              background-color: transparent;
              color: var(--color-text-dark);
          }
      </style>
      Verify Email
    </button>
  </form>

  <div id="errordiv"></div>

</div>
{% endblock content %}
