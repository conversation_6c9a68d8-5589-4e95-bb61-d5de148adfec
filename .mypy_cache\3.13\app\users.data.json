{".class": "MypyFile", "_fullname": "app.users", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AuthenticationBackend": {".class": "SymbolTableNode", "cross_ref": "fastapi_users.authentication.backend.AuthenticationBackend", "kind": "Gdef"}, "BaseUserManager": {".class": "SymbolTableNode", "cross_ref": "fastapi_users.manager.BaseUserManager", "kind": "Gdef"}, "CookieTransport": {".class": "SymbolTableNode", "cross_ref": "fastapi_users.authentication.transport.cookie.CookieTransport", "kind": "Gdef"}, "Depends": {".class": "SymbolTableNode", "cross_ref": "fastapi.param_functions.Depends", "kind": "Gdef"}, "FastAPIUsers": {".class": "SymbolTableNode", "cross_ref": "fastapi_users.fastapi_users.FastAPIUsers", "kind": "Gdef"}, "JWTStrategy": {".class": "SymbolTableNode", "cross_ref": "fastapi_users.authentication.strategy.jwt.JWTStrategy", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "starlette.requests.Request", "kind": "Gdef"}, "SECRET": {".class": "SymbolTableNode", "cross_ref": "app.config.SECRET", "kind": "Gdef"}, "SQLAlchemyUserDatabase": {".class": "SymbolTableNode", "cross_ref": "fastapi_users_db_sqlalchemy.SQLAlchemyUserDatabase", "kind": "Gdef"}, "UUIDIDMixin": {".class": "SymbolTableNode", "cross_ref": "fastapi_users.manager.UUIDIDMixin", "kind": "Gdef"}, "User": {".class": "SymbolTableNode", "cross_ref": "app.db.User", "kind": "Gdef"}, "UserManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["fastapi_users.manager.UUIDIDMixin", {".class": "Instance", "args": ["app.db.User", "uuid.UUID"], "extra_attrs": null, "type_ref": "fastapi_users.manager.BaseUserManager"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.users.UserManager", "name": "UserManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.users.UserManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.users", "mro": ["app.users.UserManager", "fastapi_users.manager.UUIDIDMixin", "fastapi_users.manager.BaseUserManager", "builtins.object"], "names": {".class": "SymbolTable", "on_after_forgot_password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "user", "token", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.users.UserManager.on_after_forgot_password", "name": "on_after_forgot_password", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "user", "token", "request"], "arg_types": ["app.users.UserManager", "app.db.User", "builtins.str", {".class": "UnionType", "items": ["starlette.requests.Request", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_after_forgot_password of UserManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_after_register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "user", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.users.UserManager.on_after_register", "name": "on_after_register", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "user", "request"], "arg_types": ["app.users.UserManager", "app.db.User", {".class": "UnionType", "items": ["starlette.requests.Request", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_after_register of UserManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_after_request_verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "user", "token", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.users.UserManager.on_after_request_verify", "name": "on_after_request_verify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "user", "token", "request"], "arg_types": ["app.users.UserManager", "app.db.User", "builtins.str", {".class": "UnionType", "items": ["starlette.requests.Request", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_after_request_verify of UserManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_after_verify": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "user", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "app.users.UserManager.on_after_verify", "name": "on_after_verify", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "user", "request"], "arg_types": ["app.users.UserManager", "app.db.User", {".class": "UnionType", "items": ["starlette.requests.Request", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "on_after_verify of UserManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "require_verification": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.users.UserManager.require_verification", "name": "require_verification", "type": "builtins.bool"}}, "reset_password_token_secret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.users.UserManager.reset_password_token_secret", "name": "reset_password_token_secret", "type": "pydantic.types.SecretStr"}}, "verification_token_secret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.users.UserManager.verification_token_secret", "name": "verification_token_secret", "type": "pydantic.types.SecretStr"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.users.UserManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.users.UserManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.users.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.users.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.users.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.users.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.users.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.users.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "auth_backend": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.users.auth_backend", "name": "auth_backend", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}], "extra_attrs": null, "type_ref": "fastapi_users.authentication.backend.AuthenticationBackend"}}}, "cookie_transport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.users.cookie_transport", "name": "cookie_transport", "type": "fastapi_users.authentication.transport.cookie.CookieTransport"}}, "create_user_settings_entry": {".class": "SymbolTableNode", "cross_ref": "app.user_settings_db.create_user_settings_entry", "kind": "Gdef"}, "current_user": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.users.current_user", "name": "current_user", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "fastapi_users": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.users.fastapi_users", "name": "fastapi_users", "type": {".class": "Instance", "args": ["app.db.User", "uuid.UUID"], "extra_attrs": null, "type_ref": "fastapi_users.fastapi_users.FastAPIUsers"}}}, "get_jwt_strategy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.users.get_jwt_strategy", "name": "get_jwt_strategy", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_jwt_strategy", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi_users.models.UP", "id": -1, "name": "models.UP", "namespace": "app.users.get_jwt_strategy", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "fastapi_users.models.UserProtocol"}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi_users.models.ID", "id": -2, "name": "models.ID", "namespace": "app.users.get_jwt_strategy", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "fastapi_users.authentication.strategy.jwt.JWTStrategy"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi_users.models.UP", "id": -1, "name": "models.UP", "namespace": "app.users.get_jwt_strategy", "upper_bound": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "fastapi_users.models.UserProtocol"}, "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "fastapi_users.models.ID", "id": -2, "name": "models.ID", "namespace": "app.users.get_jwt_strategy", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "get_user_db": {".class": "SymbolTableNode", "cross_ref": "app.db.get_user_db", "kind": "Gdef"}, "get_user_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["user_db"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator"], "fullname": "app.users.get_user_manager", "name": "get_user_manager", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["user_db"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "fastapi_users_db_sqlalchemy.SQLAlchemyUserDatabase"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_user_manager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "models": {".class": "SymbolTableNode", "cross_ref": "fastapi_users.models", "kind": "Gdef"}, "send_authorization_email": {".class": "SymbolTableNode", "cross_ref": "app.mail_utils.send_authorization_email", "kind": "Gdef"}, "send_reset_email": {".class": "SymbolTableNode", "cross_ref": "app.mail_utils.send_reset_email", "kind": "Gdef"}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}}, "path": "C:\\Local\\Projects\\BioCleaning\\app\\users.py"}